# File Naming Conventions SOP

* **Document ID:** SOP-FNC-001
* **Version:** v1.0
* **Effective Date:** 2025-07-01
* **Last Updated:** 2025-06-15
* **Author(s):** [Your Company Name]
* **Approved By:** [ORGANIZATION_NAME] Leadership/Administrator

---

## 1. Purpose

The purpose of this Standard Operating Procedure (SOP) is to establish clear and consistent guidelines for naming all digital files within [ORGANIZATION_NAME]. Adhering to these conventions will ensure files are easily searchable, quickly identifiable, reduce duplication, and improve overall data management efficiency and collaboration for all staff and volunteers.

## 2. Scope

This SOP applies to all digital files created, stored, or managed on [ORGANIZATION_NAME]'s central file system (e.g., NAS/shared drives), cloud storage, or local machines where church-related work is performed. This includes, but is not limited to, documents, spreadsheets, presentations, images, audio, and video files.

## 3. Definitions

* **SOP:** Standard Operating Procedure.
* **NAS:** Network Attached Storage; a centralized device for file storage.
* **Snake Case:** A naming convention where words are separated by underscores (e.g., `this_is_an_example`).

## 4. Procedure: File Naming Rules

Follow these rules when naming any new or existing files:

### 4.1. Date Format

Use the `YYYY-MM-DD` format for dates at the beginning of file names, where applicable.

* **General Documents:** `YYYY-MM-DD` (e.g., `2025-05-02_MeetingMinutes.docx`)
* **Recurring Documents:** For documents generated on a regular basis, include the document type after the date: `YYYY-MM-DD_DocumentType` (e.g., `2025-05-15_VestryMinutes.docx`).

### 4.2. Version Control

Manage different iterations of a document using clear versioning:

* **Draft Versions:** Append `_vXX` to the document name, where `XX` is the version number (e.g., `StrategicPlan_v01.pptx`, `StrategicPlan_v02.pptx`).
* **Final Versions:** Once a document is approved and finalized, append `_FINAL` (e.g., `StrategicPlan_FINAL.pptx`). Ensure older versions are archived or moved appropriately.

### 4.3. Naming Elements Order

Arrange elements in your file name consistently to ensure clarity and logical sorting:

**General Order:** `Date_Topic_DocumentType_Version`

* **Example:** `2025-05-15_Budget_Report_FINAL.xlsx`
* **Example (No Date):** `ProjectAlpha_Kickoff_Notes_v01.docx`

### 4.4. Characters to Avoid

To prevent compatibility issues and ensure searchability across different systems, avoid these characters:

* **No Spaces:** Use underscores (`_`) instead of spaces. (e.g., `strategic_plan_2025_draft.docx` instead of `strategic plan 2025 draft.docx`).
* **No Special Characters:** Do not use `\ / : * ? " < > | # % & { } $ ! ' @ + =` in file names. These characters often have special meanings in operating systems and can lead to errors.

### 4.5. Case Consistency

Use `snake_case` for better readability and consistency across all file names.

* **Example:** `pastoral_council_meeting_minutes_2025-06-10.docx`

---

## 5. Integration with PowerChurch

This section outlines how files related to PowerChurch data should be managed within the file system to avoid duplication and ensure proper referencing.

### 5.1. Data Flow Management

* **Export Reports:** Regularly export necessary reports from PowerChurch (e.g., financial, membership) and save them to the designated `11_POWERCHURCH_EXPORTS/` directory on the NAS.
* **Consistent Naming for Exports:** Apply the general file naming conventions to these exports. Example: `2025-06-30_PowerChurch_Financial_Summary_FINAL.pdf`.
* **No Duplication:** Do not manually create records or duplicate data in the file system that is already comprehensively managed within PowerChurch.

### 5.2. References to PowerChurch Data

When creating documents that reference data primarily managed within PowerChurch, use clear textual references rather than embedding or duplicating large datasets.

* **Example Reference:** "See PowerChurch [Module] report: [Report Name] from [Date]"
* **Specific Example:** "For current financial standing, refer to PowerChurch Accounting report: Monthly Balance Sheet from 2025-06-30."

### 5.3. PowerChurch Data vs. External Files

Understand the primary purpose of each system:

* **Use PowerChurch as the system of record for:**
  * Membership data (demographics, contact info, family relationships)
  * Contribution tracking and statements
  * Financial accounting (chart of accounts, transactions, ledgers)
  * Attendance records
  * Calendar/scheduling of events and resources
  * Employee and volunteer personal data
* **Use the central file system (NAS) for:**
  * Document drafts and final versions (e.g., strategic plans, meeting minutes, policies)
  * Supporting documentation (e.g., legal documents, contracts, agreements)
  * Media files (e.g., photos, videos, audio recordings of sermons/events)
  * Resources and templates (e.g., letterhead, presentation templates)
  * Scanned physical documents
  * Reports exported from PowerChurch

---

## 6. Roles & Responsibilities

* **All Staff & Volunteers:** Responsible for understanding and adhering to these file naming conventions when creating or saving any church-related digital files.
* **Department/Ministry Leads:** Responsible for ensuring their teams understand and follow these guidelines.
* **IT Administrator / System Manager:** Responsible for monitoring compliance, providing support, and updating this SOP as needed.

## 7. Related Documents

* SOP-FSA-002: File Structure & Navigation Guide
* SOP-NAS-003: NAS Usage & Best Practices
* SOP-DRAD-004: Data Retention, Archiving, and Deletion Policy

## 8. Revision History

| Version | Date         | Description       |
| :------ | :----------- | :---------------- |
| v1.0    | 2025-06-15   | Initial Release.  |
