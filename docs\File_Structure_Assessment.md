# [Your Organization] Digital Asset Assessment: Towards a Centralized & Secure Future

## [Introduction & Purpose of this Assessment](/File_Structure_Assessment_Notes.md)

Thank you for taking the time to complete this assessment. As a specialized consultant in IT infrastructure and data management for non-profit and religious organizations, my goal is to help your organization establish a highly efficient, secure, and intuitive digital file management system. This initiative aims to centralize your valuable digital assets, streamline operational workflows, enhance collaboration, and ensure the long-term integrity and accessibility of your records. The insights gathered from these questions will be crucial in designing a tailored solution that addresses your unique needs and challenges, empowering your team to focus more on your core ministry.

## 1. Current System Understanding

### 1.1 PowerChurch Usage & Integration Points

1. Which specific PowerChurch modules are currently in use by your organization? (e.g., Membership, Contributions, Accounting, Events).

2. What types of information do you primarily store directly within PowerChurch versus external files? How do you currently distinguish between what goes where?

3. Are there any PowerChurch reports or documents that your organization regularly exports and saves outside of PowerChurch? If so, where are these currently stored, and what is their purpose?

4. What are the key pain points or inefficiencies your organization experiences with the current PowerChurch implementation, particularly in relation to external file storage or retrieval?

### 1.2 Current File Organization & Access

1. Where are your organization's files currently stored across the organization? (e.g., individual local computers, various cloud services like Google Drive/Dropbox, shared network drives, external hard drives, etc.).

2. Can you describe your organization's current folder structure, if any, and existing file naming conventions (formal or informal)?

3. How do staff members and volunteers currently find files they need within your organization? What are the common challenges they face in locating documents?

4. Are there any existing backup procedures in place for digital files within your organization? How frequently are backups performed, and where are they stored?

5. What current permissions or access control systems are implemented for shared files within your organization? For instance, who can access which folders or documents?

### 1.3 Key Pain Points & Challenges with Current File Management

1. From your perspective, what are the biggest frustrations or inefficiencies with your organization's current digital file organization?

2. Have there been instances where important, critical, or time-sensitive files could not be located when needed by your organization? Please provide an example if possible.

3. Does your organization frequently experience issues with file version control? (e.g., multiple versions of the same document, confusion over which is the most current, accidental overwrites).

4. Approximately how much time do staff members typically spend each week or month searching for files, recreating lost documents, or resolving version conflicts within your organization?

5. Does your organization frequently find duplicate files existing across different storage locations or individual computers?

6. Are there pervasive issues with inconsistent file naming within your organization that lead to confusion or make files hard to find?

## 2. Operational Workflow & Role-Based Access

### 2.1 Document Creation, Management & Sharing

1. Which departments, ministries, or individual roles within your organization create the most digital documents?

2. What types of documents are created most frequently across your organization (e.g., meeting minutes, financial reports, event flyers, service bulletins, pastoral notes, volunteer schedules)?

3. How are documents typically shared among staff, between staff and volunteers, and with ministry leaders in your organization?

4. Are certain files or documents accessed significantly more frequently than others within your organization?

5. Does your organization have any recurring document types that are created on a regular schedule (e.g., weekly, monthly, quarterly, annually)?

### 2.2 Ministry-Specific Needs & Volunteer Access

1. Please list all active ministries at your organization.

2. Which ministries generate the most significant volume of documentation or digital files?

3. Are there seasonal ministries or events with specific, intensive file management needs?

4. How do volunteers currently access the files and documents they need for their roles within various ministries at your organization? What challenges do they face?

### 2.3 Communication & Collaboration

1. How do staff members and ministry leaders currently collaborate on documents within your organization (e.g., simultaneous editing, review/commenting, shared folders)?

2. What is the typical review and approval process for official documents within your organization (e.g., policies, communications, financial reports)?

3. How are files shared with external stakeholders, such as your organization's members, board members, or community partners?

4. Beyond PowerChurch, does your organization use any other collaboration tools (e.g., email attachments, Google Docs, Microsoft Teams, etc.)?

5. How are important meeting notes, decisions, and action items currently documented and stored within your organization?

### 2.4 Understanding Roles and Current Access Needs

To design an effective and secure system, it's vital to understand the various roles within your organization and their data access requirements.

1. Please list the primary job titles/roles within your organization (e.g., Pastor, Church Administrator, Youth Director, Financial Secretary, Volunteer Coordinator, specific Ministry Leaders, Office Assistant).

2. For each of these roles, or categories of roles (e.g., "All Staff," "All Ministry Leaders," "Specific Ministry Volunteers"), please describe:

    - What types of files or folders do they need to access? (e.g., "Pastor needs access to all administrative, pastoral, and financial reports").

    - What level of access do they require for these files? (e.g., Read-only, Can edit, Can create new files, Can delete files, Can manage permissions).

    - Which PowerChurch modules do they currently access, and with what permissions?

## 3. Technical Considerations & Future Vision

### 3.1 Storage & Remote Access

1. What is your approximate current total digital storage capacity across all systems within your organization (e.g., individual hard drives, shared drives, cloud storage accounts)?

2. Who needs remote access to files in your organization, and how is that currently handled (e.g., VPN, cloud sync, no remote access)? What are the challenges with remote access?

3. What types of devices (e.g., desktop computers, laptops, tablets, smartphones) are typically used to access your organization's files?

### 3.2 Security, Retention & Compliance

1. What types of sensitive or confidential information does your organization store digitally? (e.g., financial records, congregant personal information, pastoral counseling notes, employee HR files, donor data, historical archives).

2. Are there specific security requirements or protocols for different categories of these sensitive files within your organization?

3. Does your organization have any formal or informal compliance requirements (e.g., denominational, legal, audit) related to document retention and disposal?

4. What are your typical retention periods for different types of documents (e.g., financial records, meeting minutes, member data)?

### 3.3 Future Needs & Ideal System

1. Are there any upcoming initiatives, events, or changes at your organization that will impact your file management needs in the next 1-3 years?

2. Do you anticipate growth in specific ministries that might generate a significantly higher volume of files or require new types of digital assets for your organization?

3. If your organization could design an ideal file organization system from scratch, what would its key features and benefits be from your perspective?

4. What metrics or indicators would demonstrate that a new file organization system has been successful for your organization?

## 4. Implementation & Change Management Readiness

## 4.1 Readiness & Resources

1. Within your organization, who would be the primary point person or team responsible for collaborating on the implementation of a new file structure?

2. How much time can staff and key volunteers reasonably dedicate to assisting with file reorganization and training during the implementation phase?

3. What concerns or hesitations does your organization have about transitioning to a new, centralized file organization system?

4. In the past, what factors have prevented or hindered efforts towards better digital organization at your organization?

5. What level of training do you anticipate staff and key volunteers would need to comfortably adopt a new system?

### 4.2 Timeline Considerations

1. Approximately how many existing electronic files (e.g., in shared drives, local folders) do you estimate would need to be migrated and organized into the new system?

2. How far back do your organization's electronic records generally go? Are there specific historical records that must be preserved digitally?

3. Are there any critical deadlines or significant events in the coming months that could affect the implementation timeline or staff availability for your organization?
