<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>St. Mary's File Structure Diagram</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
        }

        .tree {
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .folder {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding: 4px 0;
            transition: all 0.2s ease;
        }

        .folder:hover {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
            padding: 4px 8px;
        }

        .folder-name {
            font-weight: bold;
            color: #2c3e50;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .folder-icon {
            width: 16px;
            height: 16px;
            transition: transform 0.2s ease;
        }

        .folder.expanded .folder-icon {
            transform: rotate(90deg);
        }

        .subfolder {
            margin-left: 24px;
            border-left: 2px solid #e9ecef;
            padding-left: 12px;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .subfolder.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .file {
            color: #666;
            margin-left: 24px;
            font-style: italic;
            position: relative;
        }

        .file:before {
            content: "📄";
            margin-right: 8px;
        }

        .level-0 { color: #e74c3c; font-size: 1.1em; }
        .level-1 { color: #3498db; }
        .level-2 { color: #27ae60; }
        .level-3 { color: #f39c12; }
        .level-4 { color: #9b59b6; }

        .description {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-left: 24px;
            font-style: italic;
        }

        .powerchurch-integration {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
        }

        .powerchurch-integration h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .powerchurch-integration ul {
            list-style: none;
            padding-left: 0;
        }

        .powerchurch-integration li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .powerchurch-integration li:before {
            content: "🔗";
            position: absolute;
            left: 0;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 5px;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>St. Mary's Moscow, ID</h1>
            <p>PowerChurch-Integrated File Structure</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="expandAll()">Expand All</button>
            <button class="btn" onclick="collapseAll()">Collapse All</button>
            <button class="btn" onclick="togglePowerChurch()">Toggle PowerChurch Info</button>
        </div>

        <div class="powerchurch-integration" id="powerchurch-info">
            <h3>🔗 PowerChurch Integration Points</h3>
            <ul>
                <li>Membership data remains in PowerChurch - export reports to folder 08</li>
                <li>Financial transactions managed in PowerChurch - export summaries as needed</li>
                <li>Contribution tracking handled by PowerChurch - statements exported to folder 08</li>
                <li>Calendar/scheduling primarily in PowerChurch - event documents in folder 06</li>
                <li>Attendance records in PowerChurch - reports exported when needed</li>
            </ul>
        </div>

        <div class="tree" id="fileTree">
            <div class="folder level-0" onclick="toggleFolder(this)">
                <span class="folder-name">
                    <span class="folder-icon">📁</span>
                    ST_MARYS/
                </span>
                <div class="description">Root directory for all St. Mary's electronic files</div>
                <div class="subfolder">
                    
                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            00_DOCUMENTATION/
                        </span>
                        <div class="description">File system guides, training materials, and policies</div>
                        <div class="subfolder">
                            <div class="file">01_File_System_Guide/</div>
                            <div class="file">02_Training_Materials/</div>
                            <div class="file">03_Policies/</div>
                            <div class="file">04_Templates/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            01_LITURGY_AND_WORSHIP/
                        </span>
                        <div class="description">Primary worship materials and liturgical resources</div>
                        <div class="subfolder">
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    01_Sunday_Services/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            2025-05_Services/
                                        </span>
                                        <div class="subfolder">
                                            <div class="file">2025-05-04_Readings.pdf</div>
                                            <div class="file">2025-05-11_Readings.pdf</div>
                                            <div class="file">2025-05-18_Readings.pdf</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    02_Music/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Sheet_Music/
                                        </span>
                                        <div class="subfolder">
                                            <div class="file">Hymns/</div>
                                            <div class="file">Contemporary/</div>
                                            <div class="file">Seasonal/</div>
                                        </div>
                                    </div>
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Playlists/
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    03_Sermons/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Scripts/
                                        </span>
                                    </div>
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Recordings/
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    04_Liturgical_Calendar/
                                </span>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    05_Special_Services/
                                </span>
                                <div class="subfolder">
                                    <div class="file">Baptisms/</div>
                                    <div class="file">Weddings/</div>
                                    <div class="file">Funerals/</div>
                                    <div class="file">Holy_Week/</div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    06_Seasonal_Programs/
                                </span>
                                <div class="subfolder">
                                    <div class="file">Advent_Christmas/</div>
                                    <div class="file">Lent_Easter/</div>
                                    <div class="file">Ordinary_Time/</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            02_ADMINISTRATION/
                        </span>
                        <div class="description">Governance, policies, and administrative documents</div>
                        <div class="subfolder">
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    01_Policy_and_Governance/
                                </span>
                                <div class="subfolder">
                                    <div class="file">Bylaws/</div>
                                    <div class="file">Policies/</div>
                                    <div class="file">Procedures/</div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    02_Meetings/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Vestry_Meetings/
                                        </span>
                                        <div class="subfolder">
                                            <div class="folder level-4" onclick="toggleFolder(this)">
                                                <span class="folder-name">
                                                    <span class="folder-icon">📁</span>
                                                    2025_Meetings/
                                                </span>
                                                <div class="subfolder">
                                                    <div class="file">2025-05-15_Agenda.pdf</div>
                                                    <div class="file">2025-05-15_Minutes.pdf</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file">Committee_Meetings/</div>
                                    <div class="file">Staff_Meetings/</div>
                                </div>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    03_Planning/
                                </span>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    04_Human_Resources/
                                </span>
                            </div>
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    05_Legal/
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            03_MINISTRIES/
                        </span>
                        <div class="description">All ministry programs including education and outreach</div>
                        <div class="subfolder">
                            <div class="file">01_Christian_Education/</div>
                            <div class="file">02_Youth/</div>
                            <div class="file">03_Children/</div>
                            <div class="file">04_Adult/</div>
                            <div class="file">05_Seniors/</div>
                            <div class="file">06_Outreach/</div>
                            <div class="file">07_Pastoral_Care/</div>
                            <div class="file">08_Ministry_Development/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            04_COMMUNICATIONS/
                        </span>
                        <div class="description">Publications, media, and external communications</div>
                        <div class="subfolder">
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    01_Weekly_Communications/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Bulletins/
                                        </span>
                                        <div class="subfolder">
                                            <div class="file">2025-05-04_Bulletin.pdf</div>
                                            <div class="file">2025-05-11_Bulletin.pdf</div>
                                        </div>
                                    </div>
                                    <div class="file">Announcements/</div>
                                    <div class="file">Prayer_Lists/</div>
                                </div>
                            </div>
                            <div class="file">02_Publications/</div>
                            <div class="file">03_Digital_Presence/</div>
                            <div class="file">04_Media/</div>
                            <div class="file">05_Branding/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            05_FACILITIES/
                        </span>
                        <div class="description">Building maintenance, equipment, and property management</div>
                        <div class="subfolder">
                            <div class="file">01_Buildings/</div>
                            <div class="file">02_Maintenance/</div>
                            <div class="file">03_Projects/</div>
                            <div class="file">04_Equipment/</div>
                            <div class="file">05_Property_Documents/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            06_EVENTS/
                        </span>
                        <div class="description">Special events, fundraisers, and community activities</div>
                        <div class="subfolder">
                            <div class="file">01_Church_Year/</div>
                            <div class="file">02_Parish_Events/</div>
                            <div class="file">03_Fundraisers/</div>
                            <div class="file">04_Community_Events/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            07_RESOURCES/
                        </span>
                        <div class="description">Templates, forms, and reference materials</div>
                        <div class="subfolder">
                            <div class="file">01_Formation/</div>
                            <div class="file">02_Worship/</div>
                            <div class="file">03_Church_Management/</div>
                            <div class="file">04_Training/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            08_POWERCHURCH_EXPORTS/
                        </span>
                        <div class="description">Reports and data exported from PowerChurch software</div>
                        <div class="subfolder">
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    01_Financial_Reports/
                                </span>
                                <div class="subfolder">
                                    <div class="folder level-3" onclick="toggleFolder(this)">
                                        <span class="folder-name">
                                            <span class="folder-icon">📁</span>
                                            Monthly_Reports/
                                        </span>
                                        <div class="subfolder">
                                            <div class="file">2025-05_Financial_Report.pdf</div>
                                            <div class="file">2025-04_Financial_Report.pdf</div>
                                        </div>
                                    </div>
                                    <div class="file">Quarterly_Reports/</div>
                                    <div class="file">Annual_Reports/</div>
                                </div>
                            </div>
                            <div class="file">02_Membership_Reports/</div>
                            <div class="file">03_Contribution_Reports/</div>
                            <div class="file">04_Backup_Files/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            09_ARCHIVE/
                        </span>
                        <div class="description">Historical records and completed projects</div>
                        <div class="subfolder">
                            <div class="folder level-2" onclick="toggleFolder(this)">
                                <span class="folder-name">
                                    <span class="folder-icon">📁</span>
                                    2024/
                                </span>
                                <div class="subfolder">
                                    <div class="file">01_LITURGY_AND_WORSHIP/</div>
                                    <div class="file">02_ADMINISTRATION/</div>
                                    <div class="file">03_MINISTRIES/</div>
                                    <div class="file">[etc.]/</div>
                                </div>
                            </div>
                            <div class="file">2023/</div>
                            <div class="file">2022/</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="legend">
            <h3 style="grid-column: 1 / -1; margin-bottom: 15px; color: #2c3e50;">Color Legend</h3>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e74c3c;"></div>
                <span>Level 0 - Root Directory</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3498db;"></div>
                <span>Level 1 - Main Categories</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #27ae60;"></div>
                <span>Level 2 - Subcategories</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f39c12;"></div>
                <span>Level 3 - Specific Folders</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b59b6;"></div>
                <span>Level 4 - Date/Project Folders</span>
            </div>
        </div>
    </div>

    <script>
        function toggleFolder(element) {
            event.stopPropagation();
            
            const subfolder = element.querySelector('.subfolder');
            if (subfolder) {
                subfolder.classList.toggle('show');
                element.classList.toggle('expanded');
            }
        }

        function expandAll() {
            const folders = document.querySelectorAll('.folder');
            const subfolders = document.querySelectorAll('.subfolder');
            
            folders.forEach(folder => folder.classList.add('expanded'));
            subfolders.forEach(subfolder => subfolder.classList.add('show'));
        }

        function collapseAll() {
            const folders = document.querySelectorAll('.folder');
            const subfolders = document.querySelectorAll('.subfolder');
            
            folders.forEach(folder => folder.classList.remove('expanded'));
            subfolders.forEach(subfolder => subfolder.classList.remove('show'));
        }

        function togglePowerChurch() {
            const info = document.getElementById('powerchurch-info');
            if (info.style.display === 'none') {
                info.style.display = 'block';
            } else {
                info.style.display = 'none';
            }
        }

        // Initially expand the root folder
        document.addEventListener('DOMContentLoaded', function() {
            const rootFolder = document.querySelector('.level-0');
            if (rootFolder) {
                const subfolder = rootFolder.querySelector('.subfolder');
                if (subfolder) {
                    subfolder.classList.add('show');
                    rootFolder.classList.add('expanded');
                }
            }
        });
    </script>
</body>
</html>