/* Church File Structure Diagram Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 20px;
}

/* Control Buttons */
.controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

/* File Tree Styles */
.tree {
    font-family: 'Courier New', monospace;
    line-height: 1.6;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #e9ecef;
    overflow-x: auto;
}

.folder {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding: 4px 0;
    transition: all 0.2s ease;
}

.folder:hover {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
}

.folder-name {
    font-weight: bold;
    color: #2c3e50;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.folder-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
}

.folder.expanded .folder-icon {
    transform: rotate(90deg);
}

.subfolder {
    margin-left: 24px;
    border-left: 2px solid #e9ecef;
    padding-left: 12px;
    display: none;
    animation: slideDown 0.3s ease;
}

.subfolder.show {
    display: block;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* File Styles */
.file {
    color: #666;
    margin-left: 24px;
    font-style: italic;
    position: relative;
}

.file:before {
    content: "📄";
    margin-right: 8px;
}

/* Hierarchy Level Colors */
.level-0 { 
    color: #e74c3c; 
    font-size: 1.1em; 
}

.level-1 { 
    color: #3498db; 
}

.level-2 { 
    color: #27ae60; 
}

.level-3 { 
    color: #f39c12; 
}

.level-4 { 
    color: #9b59b6; 
}

/* Description Text */
.description {
    font-size: 0.9em;
    color: #7f8c8d;
    margin-left: 24px;
    font-style: italic;
}

/* PowerChurch Integration Section */
.powerchurch-integration {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
}

.powerchurch-integration h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.powerchurch-integration ul {
    list-style: none;
    padding-left: 0;
}

.powerchurch-integration li {
    margin: 8px 0;
    padding-left: 20px;
    position: relative;
}

.powerchurch-integration li:before {
    content: "🔗";
    position: absolute;
    left: 0;
}

/* Legend Styles */
.legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 5px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
    }
}
